use std::path::Path;

use aeros::config::Config;
use aeros::sensors::thermal_camera::ThermalCamera;

use csv_writer::{CSVData, CSVWriter};

#[cfg(test)]
mod tests {
    use std::fs;

    use super::*;

    #[test]
    fn test_thermal_camera_new() {
        let config_file = fs::read_to_string("config.toml").expect("Failed to read config file");
        let config: Config = toml::from_str(&config_file).expect("Failed to parse config file");
        let camera = ThermalCamera::new(&config.thermal_camera)
            .expect("Failed to initialize thermal camera");
        assert_eq!(camera.as_csv_record()[0], "-1"); // Initial index should be -1
        assert!(camera.as_csv_record().len() > 2); // Should have index, timestamp, and temperatures
    }

    #[test]
    fn test_thermal_camera_refresh() {
        let config_file = fs::read_to_string("config.toml").expect("Failed to read config file");
        let config: Config = toml::from_str(&config_file).expect("Failed to parse config file");
        let mut camera = ThermalCamera::new(&config.thermal_camera)
            .expect("Failed to initialize thermal camera");
        let initial_timestamp = camera.as_csv_record()[1].clone();

        // Sleep briefly to ensure timestamp changes
        std::thread::sleep(std::time::Duration::from_millis(10));

        camera.refresh();

        // Index should increment by 1
        assert_eq!(camera.as_csv_record()[0], "0");
        // Timestamp should change
        assert_ne!(camera.as_csv_record()[1], initial_timestamp);
    }

    #[test]
    fn test_thermal_camera_csv_format() {
        let config_file = fs::read_to_string("config.toml").expect("Failed to read config file");
        let config: Config = toml::from_str(&config_file).expect("Failed to parse config file");
        let camera = ThermalCamera::new(&config.thermal_camera)
            .expect("Failed to initialize thermal camera");
        let record = camera.as_csv_record();

        // Check record structure - should have index, timestamp, and temperatures
        assert!(record.len() >= 3); // At least index, timestamp, and some temperature data

        // First element should be the index
        assert_eq!(record[0], "-1");
    }

    #[test]
    fn test_thermal_camera_csv_writer() {
        let config_file = fs::read_to_string("config.toml").expect("Failed to read config file");
        let config: Config = toml::from_str(&config_file).expect("Failed to parse config file");
        // Setup test file path
        let test_file = "test_thermal.csv";

        // Create writer
        let mut writer = CSVWriter::new(
            test_file.to_string(),
            vec![
                "index".to_string(),
                "timestamp".to_string(),
                "thermal_image".to_string(),
            ],
            ThermalCamera::new(&config.thermal_camera)
                .expect("Failed to initialize thermal camera"),
        );
        writer.write_header();

        // Write some data
        writer.write_data();

        // Verify file exists
        assert!(Path::new(test_file).exists());

        // Read file content
        let content = fs::read_to_string(test_file).unwrap();
        println!("File content:\n{}", content);

        // Basic validation
        let lines: Vec<&str> = content.lines().collect();
        assert!(lines.len() >= 2); // Header + at least 1 data row

        // Check header
        assert!(lines[0].contains("index"));
        assert!(lines[0].contains("timestamp"));
        assert!(lines[0].contains("thermal_image"));

        // Clean up
        fs::remove_file(test_file).unwrap();
    }
}
