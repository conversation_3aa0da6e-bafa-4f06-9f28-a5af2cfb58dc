use aeros::states::{CanState, StateMachine};

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_state_transitions() {
        let mut sm = StateMachine::new();

        // Initial state should be Initializing
        assert!(matches!(sm.get_state(), CanState::Initializing));

        // Test mission transition
        // First transition to Testing, then Waiting, then Mission
        sm.start_testing().unwrap();
        sm.start_waiting().unwrap();
        sm.start_mission().unwrap();
        assert!(matches!(sm.get_state(), CanState::Mission));

        // Test landing transition
        sm.start_landing().unwrap();
        assert!(matches!(sm.get_state(), CanState::Landing));
    }

    #[test]
    fn test_invalid_transitions() {
        let mut sm = StateMachine::new();

        // Cannot land from Initializing
        let result = sm.start_landing();
        assert!(result.is_err());
        assert!(matches!(sm.get_state(), CanState::Initializing));

        // Start mission (need to go through proper state transitions)
        sm.start_testing().unwrap();
        sm.start_waiting().unwrap();
        sm.start_mission().unwrap();

        // Cannot start mission again
        let result = sm.start_mission();
        assert!(result.is_err());
        assert!(matches!(sm.get_state(), CanState::Mission));
    }
}
