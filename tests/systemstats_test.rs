use std::fs;
use std::path::Path;
use std::thread;
use std::time::Duration;

use aeros::sensors::systemstats::SystemStats;
use csv_writer::CSVWriter;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_csv_writer() {
        // Setup test file path
        let test_file = "test_stats.csv";

        // Create writer
        let mut writer = CSVWriter::new(
            test_file.to_string(),
            vec![
                "index".to_string(),
                "datetime".to_string(),
                "cpu_percent".to_string(),
                "memory_percent".to_string(),
                "cpu_temp".to_string(),
                "cpu_clock".to_string(),
            ],
            SystemStats::new(),
        );
        writer.write_header();

        // Write some data
        writer.write_data();

        // Sleep briefly to ensure different timestamps
        thread::sleep(Duration::from_millis(100));

        // Write more data
        writer.write_data();

        // Verify file exists
        assert!(Path::new(test_file).exists());

        // Read file content
        let content = fs::read_to_string(test_file).unwrap();
        println!("File content:\n{}", content);

        // Basic validation
        let lines: Vec<&str> = content.lines().collect();
        assert!(lines.len() >= 3); // Header + at least 2 data rows

        // Check header
        assert!(lines[0].contains("index"));
        assert!(lines[0].contains("datetime"));
        assert!(lines[0].contains("cpu_percent"));
        assert!(lines[0].contains("memory_percent"));
        assert!(lines[0].contains("cpu_temp"));
        assert!(lines[0].contains("cpu_clock"));

        // Clean up
        fs::remove_file(test_file).unwrap();
    }

    #[test]
    fn test_index_increment() {
        // Setup test file path
        let test_file = "test_index.csv";

        // Create writer
        let mut writer = CSVWriter::new(
            test_file.to_string(),
            vec![
                "index".to_string(),
                "datetime".to_string(),
                "cpu_percent".to_string(),
                "memory_percent".to_string(),
                "cpu_temp".to_string(),
                "cpu_clock".to_string(),
            ],
            SystemStats::new(),
        );
        writer.write_header();

        // Write initial data and check index
        writer.write_data();

        // Get content and verify first index is 0
        let content = fs::read_to_string(test_file).unwrap();
        let lines: Vec<&str> = content.lines().collect();

        // Instead of assuming the format, check that the first field is "0"
        let first_data_fields: Vec<&str> = lines[1].split('|').collect();
        assert_eq!(first_data_fields[0], "0", "First index should be 0");

        // Write more data and check index increments
        writer.write_data();

        // Get updated content and verify second index is 1
        let content = fs::read_to_string(test_file).unwrap();
        let lines: Vec<&str> = content.lines().collect();

        // Check that the second data row's first field is "1"
        let second_data_fields: Vec<&str> = lines[2].split('|').collect();
        assert_eq!(second_data_fields[0], "1", "Second index should be 1");

        // Clean up
        fs::remove_file(test_file).unwrap();
    }
}
