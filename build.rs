use std::env;
use std::fs;
use std::io::Write;
use std::path::Path;

fn main() {
    let out_dir = env::var("OUT_DIR").unwrap();

    // Create a clean directory in the OUT_DIR for the XML file
    let xml_temp_dir = Path::new(&out_dir).join("methlink_xml");
    fs::create_dir_all(&xml_temp_dir).expect("Failed to create temporary directory");

    // Copy the XML file to the clean directory
    let xml_content =
        fs::read_to_string("src/xbee/methlink/methlink.xml").expect("Failed to read XML file");

    let temp_xml_path = xml_temp_dir.join("methlink.xml");
    let mut file = fs::File::create(&temp_xml_path).expect("Failed to create temporary XML file");
    file.write_all(xml_content.as_bytes())
        .expect("Failed to write to temporary XML file");

    println!("Created temporary XML file at: {}", temp_xml_path.display());

    // Use the clean directory for MAVLink generation
    match mavlink_bindgen::generate(xml_temp_dir.to_str().unwrap(), out_dir) {
        Ok(result) => {
            println!("Successfully generated MAVLink bindings");
            mavlink_bindgen::format_generated_code(&result);
            mavlink_bindgen::emit_cargo_build_messages(&result);
        }
        Err(e) => {
            // Print more detailed error and exit with a clearer message
            eprintln!("Error generating MAVLink bindings: {}", e);
            eprintln!("Check your XML file for potential issues like invalid message IDs");
            panic!("Failed to generate MAVLink bindings");
        }
    }
}
