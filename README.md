# AerOS

AerOS is our main software to control our CanSat. It runs on the
Raspberry Pi CM4 and is written in Rust.

## Features

- State machine for mission control (Testing, Waiting, Mission, Landing, Stopping states)
- System statistics monitoring (CPU usage, memory, temperature)
- Thermal camera integration with MLX90640 sensor
- CSV data logging with customizable formats
- Configurable logging system
- XBee communication using custom MAVLink dialect
- Buzzer control for landing phase

## Requirements

- Rust 2024 edition
- Raspberry Pi CM4 (or compatible) for thermal camera functionality
- Linux-based OS (for full system statistics support)
- I2C enabled for thermal camera
- XBee module connected via serial port

## Installation

Clone the repository with submodules:

```bash
git clone --recursive https://git.aerospace-lab.de/aerosat/software/aeros.git
cd aeros
```

Build the project:

```bash
cargo build --release
```

## Configuration

Create a `config.toml` file in the project root:

```toml
[logging]
level = "info"
path = "./data/logs"

[mission]
path = "./data/mission"

[xbee]
baud_rate = 9600
port = "/dev/ttyUSB0"

[methlink]
system_id = 1

[thermal_camera]
bus = "/dev/i2c-1"
addr = 0x33
frequency = 400000
fps = 2

[system_stats]
cpu_usage_threshold = 50.0
cpu_temperature_threshold = 69.0
memory_usage_threshold = 69.0

[buzzer]
channel = 0
frequency = 2.0
duty_cycle = 0.25
```

## Usage

Run the application:

```bash
cargo run --release
```

The application will:

1. Initialize all systems and start in Testing state
2. Transition to Waiting state
3. Prompt you to press Enter to start the mission
4. Transition to Mission state and collect data
5. Prompt you to press Enter to start landing
6. Transition to Landing state and activate the buzzer
7. Prompt you to press Enter to stop the mission
8. Transition to Stopping state and shut down all systems

Data will be logged to CSV files in the configured mission directory with timestamps.

## Project Structure

- `src/config.rs` - Configuration structures
- `src/csv_writer.rs` - Generic CSV writing functionality
- `src/states.rs` - Mission state machine
- `src/systemstats.rs` - System monitoring
- `src/thermal_camera.rs` - MLX90640 thermal camera interface
- `src/xbee/` - XBee communication and MAVLink implementation
- `src/buzzer.rs` - Buzzer control for landing phase
- `src/threading.rs` - Thread management for concurrent operations

## Testing

Run the test suite:

```bash
cargo test
```

## Documentation

For more detailed documentation, visit our [wiki](https://wiki.aerospace-lab.de/spaces/PROJ/pages/238649405/AerOS).
