use aeros::config::Config;
use aeros::globals::Globals;
use aeros::states::StateMachine;
use aeros::threading::{
    bme280_thread, color_camera_thread, system_stats_thread, thermal_camera_thread, xbee_thread,
};
use aeros::xbee::{components, message, systems};
use aeros::{buzzer, change_state, methlink};

use chrono::Local;

use std::fs::DirBuilder;
use std::fs::{self, File};
use std::path::Path;

use toml;

use log::LevelFilter;
#[allow(unused_imports)]
use log::{error, info, warn};
use simplelog::{ColorChoice, CombinedLogger, TermLogger, TerminalMode, WriteLogger};

use std::thread;

fn main() {
    let config_file = fs::read_to_string("config.toml").expect("Failed to read config file");
    let config: Config = toml::from_str(&config_file).expect("Failed to parse config file");

    let mission_path = format!(
        "{}-{}",
        &config.mission.path,
        Local::now().format("%Y-%m-%d_%H-%M-%S")
    );

    let base_dir = Path::new(&config.mission.path)
        .parent()
        .unwrap_or(Path::new("."));
    if !base_dir.exists() {
        DirBuilder::new()
            .recursive(true)
            .create(base_dir)
            .expect("Couldn't create base data directory!");
    }

    if !Path::new(&mission_path).exists() {
        DirBuilder::new()
            .recursive(true)
            .create(&mission_path)
            .expect("Couldn't create mission directory!");
    }

    let log_level = config
        .logging
        .level
        .parse::<LevelFilter>()
        .unwrap_or(LevelFilter::Info);
    let log_path = &config.logging.path;

    let log_file = format!(
        "{}/aeros-{}.log",
        log_path,
        Local::now().format("%Y-%m-%d_%H-%M-%S")
    );

    if !Path::new(log_path).exists() {
        DirBuilder::new()
            .recursive(true)
            .create(log_path)
            .expect("Couldn't create logs directory!");
    }

    CombinedLogger::init(vec![
        TermLogger::new(
            LevelFilter::Warn,
            simplelog::Config::default(),
            TerminalMode::Mixed,
            ColorChoice::Auto,
        ),
        WriteLogger::new(
            log_level,
            simplelog::Config::default(),
            File::create(log_file).expect("Couldn't create logfile!"),
        ),
    ])
    .expect("Couldn't setup combined logger!");

    info!("Statemachine initialized");
    let mission_state = StateMachine::new();

    let (xbee_sender, xbee_receiver) = std::sync::mpsc::channel::<message::XbeeMessage>();

    #[allow(unused_mut)]
    let mut globals = Globals::new(mission_path, mission_state, config, xbee_sender);

    let globals_xbee = globals.clone();
    let xbee_thread = thread::spawn(move || xbee_thread(globals_xbee, xbee_receiver));

    let globals_system_stats = globals.clone();
    let system_stats_thread = thread::spawn(move || system_stats_thread(globals_system_stats));

    let globals_thermal_camera = globals.clone();
    let thermal_camera_thread =
        thread::spawn(move || thermal_camera_thread(globals_thermal_camera));

    let globals_color_camera = globals.clone();
    let color_camera_thread = thread::spawn(move || color_camera_thread(globals_color_camera));

    let globals_bme280 = globals.clone();
    let bme280_thread = thread::spawn(move || bme280_thread(globals_bme280));

    let buzzer = buzzer::initialize_buzzer(&globals).unwrap_or_else(|err| {
        error!("Failed to initialize buzzer: {}", err);
        None
    });

    change_state!(globals.clone(), start_testing);

    globals
        .xbee_sender
        .send(message::XbeeMessage::new(
            systems::XbeeSystems::ComputeModule(components::ComputeModuleComponents::Main),
            methlink::MavMessage::WARNING(methlink::WARNING_DATA {
                warning_code: methlink::Warning::WARNING_TEST,
            }),
        ))
        .unwrap();

    globals
        .xbee_sender
        .send(message::XbeeMessage::new(
            systems::XbeeSystems::ComputeModule(components::ComputeModuleComponents::Main),
            methlink::MavMessage::ERROR(methlink::ERROR_DATA {
                error_code: methlink::Error::ERROR_TEST,
            }),
        ))
        .unwrap();

    change_state!(globals.clone(), start_waiting);

    let mut input = String::new();

    println!("Enter to start mission");
    while !std::io::stdin().read_line(&mut input).is_ok() {
        thread::sleep(std::time::Duration::from_secs(1));
    }

    change_state!(globals.clone(), start_mission);

    println!("Enter to start landing");
    while !std::io::stdin().read_line(&mut input).is_ok() {
        thread::sleep(std::time::Duration::from_secs(1));
    }

    change_state!(globals.clone(), start_landing);

    buzzer::start_bussin(&buzzer, &globals).unwrap_or_else(|err| {
        error!("Failed to start buzzer: {}", err);
    });

    println!("Enter to start stopping");
    while !std::io::stdin().read_line(&mut input).is_ok() {
        thread::sleep(std::time::Duration::from_secs(1));
    }

    change_state!(globals.clone(), start_stopping);

    buzzer::stop_bussin(&buzzer, &globals).unwrap_or_else(|err| {
        error!("Failed to stop buzzer: {}", err);
    });

    system_stats_thread.join().unwrap();
    bme280_thread.join().unwrap();
    thermal_camera_thread.join().unwrap();
    color_camera_thread.join().unwrap();
    xbee_thread.join().unwrap();
}
