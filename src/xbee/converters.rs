//! Contains functions to convert internally used types to the types used by methlink.

use crate::methlink;
use crate::states::CanState;

/// Converts the CanSat state to the methlink state.
pub fn state_from_pseudo(canstate: &CanState) -> methlink::CanState {
    match canstate {
        CanState::Initializing => methlink::CanState::MAV_STATE_INITIALIZING,
        CanState::Testing => methlink::CanState::MAV_STATE_TESTING,
        CanState::Waiting => methlink::CanState::MAV_STATE_WAITING,
        CanState::Mission => methlink::CanState::MAV_STATE_MISSION,
        CanState::Landing => methlink::CanState::MAV_STATE_LANDING,
        CanState::Stopping => methlink::CanState::MAV_STATE_STOPPING,
    }
}
