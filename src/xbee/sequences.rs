//! This module contains the sequence struct and the sequence functions.
//!
//! It is used to keep track of the sequence numbers for the different components.
//! Components are defined [here](https://wiki.aerospace-lab.de/spaces/PROJ/pages/234192902/Methlink).

use crate::xbee::components::{ComputeModuleComponents, FlywheelComponents};
use crate::xbee::systems::XbeeSystems;

use std::collections::HashMap;
use std::sync::{Arc, Mutex};

use strum::IntoEnumIterator;

/// Type alias for the sequence number.
pub type Sequence = u8;

/// Type alias for the sequences struct.
pub type Sequences<T> = Arc<Mutex<HashMap<T, Sequence>>>;

/// Struct to represent the sequences for the different components.
/// It coounts the number of messages sent from a specific component.
/// It can be used to detect packet loss.
#[derive(Debug, Clone)]
pub struct XbeeSequences {
    compute_module_components: Sequences<ComputeModuleComponents>,
    flywheel_components: Sequences<FlywheelComponents>,
}

impl XbeeSequences {
    pub fn new() -> XbeeSequences {
        XbeeSequences {
            compute_module_components: Arc::new(Mutex::new(HashMap::new())),
            flywheel_components: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    pub fn get_sequence(&self, system: &XbeeSystems) -> Result<Sequence, anyhow::Error> {
        match system {
            XbeeSystems::ComputeModule(component) => self
                .compute_module_components
                .lock()
                .map_err(|e| anyhow::anyhow!("Failed to lock compute module sequences: {}", e))?
                .get(component)
                .copied()
                .ok_or_else(|| {
                    anyhow::anyhow!(
                        "Sequence for compute module component {:?} not found",
                        component
                    )
                }),
            XbeeSystems::Flywheel(component) => self
                .flywheel_components
                .lock()
                .map_err(|e| anyhow::anyhow!("Failed to lock flywheel sequences: {}", e))?
                .get(component)
                .copied()
                .ok_or_else(|| {
                    anyhow::anyhow!("Sequence for flywheel component {:?} not found", component)
                }),
        }
    }

    pub fn set_sequence(&mut self, component: &XbeeSystems, sequence: Sequence) {
        match component {
            XbeeSystems::ComputeModule(component) => {
                self.compute_module_components
                    .lock()
                    .unwrap()
                    .insert(*component, sequence);
            }
            XbeeSystems::Flywheel(component) => {
                self.flywheel_components
                    .lock()
                    .unwrap()
                    .insert(*component, sequence);
            }
        }
    }
    pub fn increment_sequence(&mut self, component: &XbeeSystems) {
        match component {
            XbeeSystems::ComputeModule(component) => {
                self.set_sequence(
                    &XbeeSystems::ComputeModule(*component),
                    self.get_sequence(&XbeeSystems::ComputeModule(*component))
                        .unwrap()
                        + 1,
                );
            }
            XbeeSystems::Flywheel(component) => {
                self.set_sequence(
                    &XbeeSystems::Flywheel(*component),
                    self.get_sequence(&XbeeSystems::Flywheel(*component))
                        .unwrap()
                        + 1,
                );
            }
        }
    }
}

impl Default for XbeeSequences {
    fn default() -> Self {
        let mut sequences = XbeeSequences::new();
        for component in ComputeModuleComponents::iter() {
            sequences.set_sequence(&XbeeSystems::ComputeModule(component), 0);
        }
        for component in FlywheelComponents::iter() {
            sequences.set_sequence(&XbeeSystems::Flywheel(component), 0);
        }
        sequences
    }
}
