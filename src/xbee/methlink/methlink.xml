<?xml version="1.0"?>
<mavlink>
  <version>3</version>
  <dialect>0</dialect>
  <enums>
    <enum name="CAN_STATE">
        <description>System state</description>
        <entry value="0" name="MAV_STATE_INITIALIZING">
            <description>Initializing</description>
        </entry>
        <entry value="1" name="MAV_STATE_TESTING">
            <description>Testing</description>
        </entry>
        <entry value="2" name="MAV_STATE_WAITING">
            <description>Waiting</description>
        </entry>
        <entry value="3" name="MAV_STATE_MISSION">
            <description>Mission</description>
        </entry>
        <entry value="4" name="MAV_STATE_LANDING">
            <description>Landing</description>
        </entry>
        <entry value="5" name="MAV_STATE_STOPPING">
            <description>Stopping</description>
        </entry>
    </enum>
    <enum name="ERROR">
        <description>Error codes</description>
        <entry value="0" name="ERROR_TEST">
            <description>No error has occurred</description>
        </entry>
        <entry value="1" name="ERROR_INVALID_STATE_TRANSITION">
            <description>An invalid state transition has been tried</description>
        </entry>
        <entry value="2" name="ERROR_THERMAL_CAMERA_INITIALIZATION_FAILED">
            <description>The thermal camera failed to initialize</description>
        </entry>
        <entry value="3" name="ERROR_INVALID_BUZZER_CHANNEL">
            <description>Invalid buzzer channel</description>
        </entry>
        <entry value="4" name="ERROR_BUZZER_INITIALIZATION_FAILED">
            <description>The buzzer failed to initialize</description>
        </entry>
        <entry value="5" name="ERROR_BUZZER_ENABLE_FAILED">
            <description>The buzzer failed to enable</description>
        </entry>
        <entry value="6" name="ERROR_BUZZER_DISABLE_FAILED">
            <description>The buzzer failed to disable</description>
        </entry>
        <entry value="7" name="ERROR_BME280_INITIALIZATION_FAILED">
            <description>The BME280 failed to initialize</description>
        </entry>
        <entry value="8" name="ERROR_GPS_INITIALIZATION_FAILED">
            <description>The GPS failed to initialize</description>
        </entry>
    </enum>
    <enum name="WARNING">
        <description>Warning codes</description>
        <entry value="0" name="WARNING_TEST">
            <description>No warning has occurred</description>
        </entry>
        <entry value="1" name="WARNING_CPU_TEMPERATURE_HIGH">
            <description>CPU temperature is too high</description>
        </entry>
        <entry value="2" name="WARNING_MEMORY_USAGE_HIGH">
            <description>Memory usage is too high</description>
        </entry>
        <entry value="3" name="WARNING_CPU_USAGE_HIGH">
            <description>CPU usage is too high</description>
        </entry>
    </enum>
  </enums>
  <messages>
    <message id="101" name="BME280">
      <description>BME280 data</description>
      <field type="float" name="temperature">Temperature</field>
      <field type="float" name="pressure">Pressure</field>
      <field type="float" name="humidity">Humidity</field>
    </message>
    <message id="102" name="HEARTBEAT">
      <description>Heartbeat</description>
      <field type="uint8_t" name="state" enum="CAN_STATE">State</field>
    </message>
    <message id="103" name="SYSTEM_STATS">
      <description>System statistics</description>
      <field type="float" name="cpu_percentage">CPU percentage</field>
      <field type="float" name="memory_percentage">Memory percentage</field>
      <field type="float" name="cpu_temperature">CPU temperature</field>
      <field type="uint64_t" name="cpu_clock">CPU clock</field>
    </message>
    <message id="104" name="CAN_STATE_CHANGE">
      <description>CanSat state change</description>
      <field type="uint8_t" name="from_state" enum="CAN_STATE"></field>
      <field type="uint8_t" name="to_state" enum="CAN_STATE"></field>
    </message>
    <message id="105" name="ERROR">
      <description>Error</description>
      <field type="uint8_t" name="error_code" enum="ERROR">Error code</field>
    </message>
    <message id="106" name="WARNING">
      <description>Warning</description>
      <field type="uint8_t" name="warning_code" enum="WARNING">Warning code</field>
    </message>
  </messages>
</mavlink>
