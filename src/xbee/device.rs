//! This module is to interface with the Xbee communication hardware.
//!
//! It interfaces with the Xbee module S2C Pro.
//!
//! ## Links
//! - [Datasheet](https://www.mouser.com/pdfdocs/ds_xbee-s2c-802-15-4.pdf)
//! - [Manual](https://docs.digi.com/resources/documentation/digidocs/pdfs/90002002.pdf)

use log::warn;
use serialport::SerialPort;
use std::fs::File;
use std::io::Write;
use std::sync::{Arc, Mutex};

/// Enum to represent the Xbee device but also its fallback.
enum SerialDevice {
    Xbee(Box<dyn SerialPort>),
    Fallback(File),
}

/// XbeeDevice struct containing a thread-safe instance of the SerialDevice enum.
pub struct XbeeDevice {
    device: Arc<Mutex<SerialDevice>>,
}

impl XbeeDevice {
    /// Creates a new XbeeDevice instance with the given configuration.
    pub fn new(
        port: &str,
        baud_rate: u32,
        fallback_path: &str,
    ) -> Result<XbeeDevice, anyhow::Error> {
        match serialport::new(port, baud_rate).open() {
            Ok(port) => Ok(XbeeDevice {
                device: Arc::new(Mutex::new(SerialDevice::Xbee(port))),
            }),
            Err(e) => {
                warn!("Failed to open Xbee port, using fallback: {}", e);
                match File::create(format!("{}/xbee_fallback.txt", fallback_path)) {
                    Ok(fallback_file) => Ok(XbeeDevice {
                        device: Arc::new(Mutex::new(SerialDevice::Fallback(fallback_file))),
                    }),
                    Err(e) => Err(anyhow::anyhow!("Failed to create fallback file: {}", e)),
                }
            }
        }
    }
}

impl Write for XbeeDevice {
    fn write_all(&mut self, buf: &[u8]) -> std::io::Result<()> {
        match *self.device.lock().unwrap() {
            SerialDevice::Xbee(ref mut port) => {
                port.write_all(buf)?;
                Ok(())
            }
            SerialDevice::Fallback(ref mut file) => {
                file.write_all(buf)?;
                Ok(())
            }
        }
    }
    fn flush(&mut self) -> std::io::Result<()> {
        match *self.device.lock().unwrap() {
            SerialDevice::Xbee(ref mut port) => port.flush(),
            SerialDevice::Fallback(ref mut file) => file.flush(),
        }
    }
    fn write(&mut self, buf: &[u8]) -> std::io::Result<usize> {
        match *self.device.lock().unwrap() {
            SerialDevice::Xbee(ref mut port) => port.write(buf),
            SerialDevice::Fallback(ref mut file) => file.write(buf),
        }
    }
}
