//! This module contains the systems enum.
//!
//! It is used to differentiate between the different systems on the CanSat.
//! Systems are defined [here](https://wiki.aerospace-lab.de/spaces/PROJ/pages/234192902/Methlink).

use crate::xbee::components::{ComputeModuleComponents, FlywheelComponents};

/// Enum to represent the different systems on the CanSat.
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq)]
pub enum XbeeSystems {
    ComputeModule(ComputeModuleComponents),
    Flywheel(FlywheelComponents),
}
