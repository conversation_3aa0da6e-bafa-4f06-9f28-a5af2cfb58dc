//! The module contains the structural layout of the CanSat specified
//! [here](https://wiki.aerospace-lab.de/spaces/PROJ/pages/234192902/Methlink).

use num_derive::ToPrimitive;
use strum_macros::EnumIter;

/// Components of the compute module.
///
/// Components are all important parts of the CanSat. Such as Sensors and Actuators. They are need to identify
/// the source of a message and packet loss for specific components.
///
/// These are all Components running on the Compute Module.
#[derive(Debug, Clone, Copy, PartialEq, Eq, ToPrimitive, EnumIter, Hash)]
pub enum ComputeModuleComponents {
    Main = 0,
    StateMachine = 1,
    ThermalCamera = 2,
    SystemStats = 3,
    Camera = 4,
    Bme280 = 5,
    Buzzer = 6,
    GPS = 7,
}

/// Components of the flywheel module.
///
/// Components are all important parts of the CanSat. Such as Sensors and Actuators. They are need to identify
/// the source of a message and packet loss for specific components.
///
/// These are all Components running on the Flywheel Ardunio.
///
/// ## Links
/// - [Flywheel](https://wiki.aerospace-lab.de/spaces/PROJ/pages/199524370/FlyWheel)
#[derive(Debug, Clone, Copy, PartialEq, Eq, ToPrimitive, EnumIter, Hash)]
pub enum FlywheelComponents {
    Flywheel = 0,
    Accelerometer = 1,
}
