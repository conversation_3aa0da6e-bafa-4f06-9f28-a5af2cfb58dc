//! This module contains the message struct and the cook function to send messages to the methlink.

use crate::globals;
use crate::methlink::MavMessage;
use crate::xbee::device::XbeeDevice;
use crate::xbee::systems::XbeeSystems;

use num_traits::ToPrimitive;

/// Struct to represent a message to be sent to the methlink.
///
/// It is a abstraction over the mavlink message to make it easier to send messages.
///
pub struct XbeeMessage {
    system: XbeeSystems,
    message: MavMessage,
}

impl XbeeMessage {
    /// Creates a new XbeeMessage instance.
    pub fn new(system: XbeeSystems, message: MavMessage) -> XbeeMessage {
        XbeeMessage { system, message }
    }

    /// Sends the message to xbee.
    pub fn cook(
        &self,
        xbee_device: &mut XbeeDevice,
        globals: globals::Globals,
    ) -> Result<usize, anyhow::Error> {
        match self.system {
            XbeeSystems::ComputeModule(component) => mavlink::write_v1_msg(
                xbee_device,
                mavlink::MavHeader {
                    system_id: 0,
                    component_id: component.to_u8().unwrap(),
                    sequence: globals.sequences.get_sequence(&self.system)?,
                },
                &self.message,
            )
            .map_err(|e| anyhow::anyhow!("Failed to write message: {}", e)),
            XbeeSystems::Flywheel(component) => mavlink::write_v1_msg(
                xbee_device,
                mavlink::MavHeader {
                    system_id: 1,
                    component_id: component.to_u8().unwrap(),
                    sequence: globals.sequences.get_sequence(&self.system)?,
                },
                &self.message,
            )
            .map_err(|e| anyhow::anyhow!("Failed to write message: {}", e)),
        }
    }
}
