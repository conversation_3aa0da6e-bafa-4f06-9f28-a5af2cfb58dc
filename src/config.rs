//! This module contains the configuration structure for the application.

use serde::Deserialize;

/// Main configuration structure for the application.
#[derive(Debug, Deserialize, <PERSON>lone)]
pub struct Config {
    /// Logging configuration settings
    pub logging: Logging,
    /// Mission configuration settings
    pub mission: Mission,
    /// Xbee communication configuration settings
    pub xbee: Xbee,
    /// Methlink configuration settings
    pub methlink: Methlink,
    /// Thermal camera configuration settings
    pub thermal_camera: ThermalCamera,
    /// System statistics configuration settings
    pub system_stats: SystemStats,
    /// Buzzer configuration settings
    pub buzzer: Buzzer,
    /// Color camera configuration settings
    pub color_camera: ColorCamera,
    /// BME280 configuration settings
    pub bme280: Bme280,
    /// GPS configuration settings
    pub gps: Gps,
}

/// Configuration for the logging system.
#[derive(Debug, Deserialize, Clone)]
pub struct Logging {
    /// Log level (e.g., "info", "debug", "warn", "error")
    pub level: String,
    /// Directory path where log files will be stored
    pub path: String,
}

/// Configuration for the mission system.
#[derive(Debug, Deserialize, <PERSON><PERSON>)]
pub struct Mission {
    /// Directory path where mission files will be stored
    pub path: String,
}

/// Configuration for the Xbee communication system.
#[derive(Debug, Deserialize, Clone)]
pub struct Xbee {
    /// Baud rate for the Xbee communication
    pub baud_rate: u32,
    /// Port for the Xbee communication
    pub port: String,
}

/// Configuration for the Methlink communication system.
#[derive(Debug, Deserialize, Clone)]
pub struct Methlink {
    /// System ID for the Methlink communication
    pub system_id: u8,
}

/// Configuration for the MLX90640 thermal camera.
#[derive(Debug, Deserialize, Clone)]
pub struct ThermalCamera {
    /// I2C bus path for the thermal camera
    pub bus: String,
    /// I2C address for the thermal camera
    pub addr: u8,
    /// I2C frequency for the thermal camera
    pub frequency: u32,
    /// Frames per second for the thermal camera
    pub fps: u32,
}

/// Configuration for the system statistics.
#[derive(Debug, Deserialize, Clone)]
pub struct SystemStats {
    /// CPU usage threshold
    pub cpu_usage_threshold: f32,
    /// CPU temperature threshold
    pub cpu_temperature_threshold: f32,
    /// Memory usage threshold
    pub memory_usage_threshold: f32,
}

#[derive(Debug, Deserialize, Clone)]
pub struct Buzzer {
    /// PWM channel for the buzzer
    pub channel: u8,
    /// Frequency for the buzzer
    pub frequency: f64,
    /// Duty cycle for the buzzer
    pub duty_cycle: f64,
}

#[derive(Debug, Deserialize, Clone)]
pub struct ColorCamera {
    /// Width for the color camera
    pub width: u32,
    /// Height for the color camera
    pub height: u32,
    /// Frames per second for the color camera
    pub fps: u32,
}

#[derive(Debug, Deserialize, Clone)]
pub struct Bme280 {
    /// I2C address for the BME280
    pub address: u8,
    /// I2C bus path for the BME280
    pub i2c_bus: String,
    /// Frames per second for the BME280
    pub fps: u64,
}

#[derive(Debug, Deserialize, Clone)]
pub struct Gps {
    /// Port for the GPS
    pub port: String,
    /// Baud rate for the GPS
    pub baud_rate: u32,
}
