//! This module is to interface with the color camera hardware.

use chrono::{DateTime, Local};
use log::info;
use std::fs;
use turbojpeg::{Compressor, Image, PixelFormat as TjPixelFormat};

/// Saves a frame to a JPEG file.
pub fn save_frame(
    yuyv_data: Vec<u8>,
    bytes_used: usize,
    path: String,
    framecount: u32,
    width: usize,
    height: usize,
) -> Result<(), Box<dyn std::error::Error>> {
    let now: DateTime<Local> = Local::now();

    // Calculate expected buffer size for YUYV format (2 bytes per pixel)
    let expected_size = width * height * 2;
    if bytes_used != expected_size {
        info!(
            "Warning: Buffer size mismatch - got {} bytes, expected {} bytes",
            bytes_used, expected_size
        );
        info!("Dimensions: {}x{}", width, height);
    }

    let mut compressor = Compressor::new()?;

    // Convert YUYV to RGB
    let mut rgb_data = vec![0u8; width * height * 3];
    yuyv_to_rgb(&yuyv_data, &mut rgb_data, width, height);

    let rgb_image = Image {
        pixels: rgb_data.as_slice(),
        width,
        height,
        format: TjPixelFormat::RGB,
        pitch: width * 3,
    };

    let jpeg_buffer_size = (width * height * 3) / 4;
    let mut jpeg_buffer = turbojpeg::OutputBuf::allocate_owned(jpeg_buffer_size);

    compressor.set_quality(85)?;
    compressor.compress(rgb_image, &mut jpeg_buffer)?;

    let timestamp = now.format("%Y-%m-%d_%H-%M-%S");
    let filename = format!("{}/{}_color_camera_{}.jpg", path, framecount, timestamp);
    fs::write(&filename, jpeg_buffer.as_mut())?;
    info!("Written {} bytes to {}", jpeg_buffer.len(), &filename);

    Ok(())
}

// Helper function to convert YUYV to RGB
fn yuyv_to_rgb(yuyv: &[u8], rgb: &mut [u8], width: usize, height: usize) {
    for y in 0..height {
        for x in 0..(width / 2) {
            let i = y * width * 2 + x * 4;
            let y1 = yuyv[i] as f32;
            let u = yuyv[i + 1] as f32 - 128.0;
            let y2 = yuyv[i + 2] as f32;
            let v = yuyv[i + 3] as f32 - 128.0;

            // First pixel
            let r1 = y1 + 1.402 * v;
            let g1 = y1 - 0.344136 * u - 0.714136 * v;
            let b1 = y1 + 1.772 * u;

            // Second pixel
            let r2 = y2 + 1.402 * v;
            let g2 = y2 - 0.344136 * u - 0.714136 * v;
            let b2 = y2 + 1.772 * u;

            let j = y * width * 3 + x * 6;

            // Clamp values between 0 and 255
            rgb[j] = r1.clamp(0.0, 255.0) as u8;
            rgb[j + 1] = g1.clamp(0.0, 255.0) as u8;
            rgb[j + 2] = b1.clamp(0.0, 255.0) as u8;
            rgb[j + 3] = r2.clamp(0.0, 255.0) as u8;
            rgb[j + 4] = g2.clamp(0.0, 255.0) as u8;
            rgb[j + 5] = b2.clamp(0.0, 255.0) as u8;
        }
    }
}
