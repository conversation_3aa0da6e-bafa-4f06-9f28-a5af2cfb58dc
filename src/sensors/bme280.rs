//! This module is to interface with the BME280 sensor hardware.

    use csv_writer::CSVData;

use anyhow::Result;
use chrono::Local;

use bme280::i2c::BME280;
use linux_embedded_hal::{Delay, I2cdev};

/// Struct to hold BME280 data.
pub struct Bme280 {
    bme280: BME280<I2cdev>,
    index: i32,
    timestamp: chrono::DateTime<Local>,
    pub temperature: f32,
    pub pressure: f32,
    pub humidity: f32,
}

impl Bme280 {
    pub fn new(config: &crate::config::Bme280) -> Result<Self, anyhow::Error> {
        let i2c = I2cdev::new(config.i2c_bus.as_str())?;
        let mut bme280 = BME280::new(i2c, config.address);
        bme280
            .init(&mut Delay)
            .map_err(|_| anyhow::anyhow!("Failed to initialize BME280"))?;
        Ok(Bme280 {
            bme280: bme280,
            index: -1,
            timestamp: Local::now(),
            temperature: 0.0,
            pressure: 0.0,
            humidity: 0.0,
        })
    }
    fn get_index(&self) -> i32 {
        self.index + 1
    }
}

impl CSVData for Bme280 {
    fn refresh(&mut self) {
        let measurement = self.bme280.measure(&mut Delay).unwrap();
        self.index = self.get_index();
        self.timestamp = Local::now();
        self.temperature = measurement.temperature;
        self.pressure = measurement.pressure;
        self.humidity = measurement.humidity;
    }
    fn as_csv_record(&self) -> Vec<String> {
        vec![
            self.index.to_string(),
            self.timestamp.to_string(),
            self.temperature.to_string(),
            self.pressure.to_string(),
            self.humidity.to_string(),
        ]
    }
}
