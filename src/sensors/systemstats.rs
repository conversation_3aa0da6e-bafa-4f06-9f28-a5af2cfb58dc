//! This module is to collect system statistics as debug.

use csv_writer::CSVData;

use chrono::Local;
use log::warn;
use sysinfo::System;
use systemstat::Platform;

/// Struct to hold system statistics.
#[derive(Debug, PartialEq, PartialOrd)]
pub struct SystemStats {
    index: i32,
    timestamp: chrono::DateTime<Local>,
    pub cpu_percentage: f32,
    pub memory_percentage: f32,
    pub cpu_temperature: f32,
    pub cpu_clock: u64,
}

/// Implementation of SystemStats
impl SystemStats {
    /// Creates a new instance of SystemStats.
    pub fn new() -> Self {
        SystemStats {
            index: -1,
            timestamp: Local::now(),
            cpu_percentage: Self::get_cpu_percentage(),
            memory_percentage: Self::get_memory_usage(),
            cpu_temperature: Self::get_cpu_temperature(),
            cpu_clock: Self::get_cpu_clock(),
        }
    }
    /// Returns the next index value.
    fn get_index(&self) -> i32 {
        self.index + 1
    }

    /// Returns the CPU percentage.
    fn get_cpu_percentage() -> f32 {
        let mut sys = System::new();
        sys.refresh_cpu_usage();

        sys.global_cpu_usage()
    }

    /// Returns the memory percentage.
    fn get_memory_usage() -> f32 {
        let mut sys = System::new();
        sys.refresh_memory();

        let memory_percentage = sys.used_memory() as f32 / sys.total_memory() as f32 * 100_f32;
        memory_percentage
    }

    /// Returns the CPU temperature.
    fn get_cpu_temperature() -> f32 {
        let sys = systemstat::System::new();
        match sys.cpu_temp() {
            Ok(temp) => temp,
            Err(error) => {
                warn!("Error: {}", error);
                -1000.0
            }
        }
    }

    /// Returns the CPU clock.
    fn get_cpu_clock() -> u64 {
        let mut sys = System::new();
        sys.refresh_cpu_frequency();
        let mut frequencies: Vec<u64> = Vec::new();

        for cpu in sys.cpus() {
            frequencies.push(cpu.frequency());
        }

        let sum: u64 = frequencies.iter().sum();
        let count = frequencies.len() as u64;
        let average = sum / count;

        average
    }
}

/// Implementation of CSVData for SystemStats
impl CSVData for SystemStats {
    fn refresh(&mut self) {
        self.index = self.get_index();
        self.timestamp = Local::now();
        self.cpu_percentage = Self::get_cpu_percentage();
        self.memory_percentage = Self::get_memory_usage();
        self.cpu_temperature = Self::get_cpu_temperature();
        self.cpu_clock = Self::get_cpu_clock();
    }
    fn as_csv_record(&self) -> Vec<String> {
        vec![
            self.index.to_string(),
            self.timestamp.to_string(),
            self.cpu_percentage.to_string(),
            self.memory_percentage.to_string(),
            self.cpu_temperature.to_string(),
            self.cpu_clock.to_string(),
        ]
    }
}
