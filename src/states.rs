//! This module contains the state machine for the CanSat.
//!
//! The state machine manages transitions between different operational states
//! of the CanSat during its mission lifecycle, from initialization to landing.

use std::fmt::{Debug, Display, Formatter, Result};
use std::marker::{Send, Sync};
use std::sync::{Arc, Mutex};

/// Represents the different states the CanSat can be in.
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum CanState {
    /// Initializing state for initializing the CanSat
    Initializing,
    /// Testing state for testing before launch
    Testing,
    /// Initial state before mission begins
    Waiting,
    /// Active mission state where all Sensors are active
    Mission,
    /// Landing: Final state when CanSat has landed
    Landing,
    /// Stopping: Final state when CanSat has stopped
    Stopping,
}

impl Display for CanState {
    fn fmt(&self, f: &mut Formatter<'_>) -> Result {
        match self {
            CanState::Initializing => write!(f, "Initializing"),
            CanState::Testing => write!(f, "Testing"),
            CanState::Waiting => write!(f, "Waiting"),
            CanState::Mission => write!(f, "Mission"),
            CanState::Landing => write!(f, "Landing"),
            CanState::Stopping => write!(f, "Stopping"),
        }
    }
}

/// Main CanSat state machine that manages state transitions.
#[derive(Debug, Clone)]
pub struct StateMachine {
    /// Current state of the CanSat, wrapped in thread-safe containers
    state: Arc<Mutex<CanState>>,
}

impl StateMachine {
    /// Creates a new state machine initialized to the Initializing state.
    pub fn new() -> StateMachine {
        StateMachine {
            state: Arc::new(Mutex::new(CanState::Initializing)),
        }
    }

    /// Transitions from Initializing to Testing state.
    ///
    /// # Errors
    ///
    /// Returns an error if called when not in Initializing state or if
    /// the transition is otherwise invalid.
    pub fn start_testing(&mut self) -> anyhow::Result<()> {
        let mut state = self.state.lock().unwrap();
        match *state {
            CanState::Initializing => {
                *state = CanState::Testing;
                Ok(())
            }
            CanState::Testing => Err(anyhow::anyhow!("Testing already started")),
            _ => Err(anyhow::anyhow!("Invalid state transition")),
        }
    }

    /// Transitions from Testing to Waiting state.
    ///
    /// # Errors
    ///
    /// Returns an error if called when not in Testing state or if
    /// the transition is otherwise invalid.
    pub fn start_waiting(&mut self) -> anyhow::Result<()> {
        let mut state = self.state.lock().unwrap();
        match *state {
            CanState::Testing => {
                *state = CanState::Waiting;
                Ok(())
            }
            CanState::Waiting => Err(anyhow::anyhow!("Waiting already started")),
            _ => Err(anyhow::anyhow!("Invalid state transition")),
        }
    }

    /// Transitions from Waiting to Mission state.
    ///
    /// # Errors
    ///
    /// Returns an error if called when not in Waiting state or if
    /// the transition is otherwise invalid.
    pub fn start_mission(&mut self) -> anyhow::Result<()> {
        let mut state = self.state.lock().unwrap();
        match *state {
            CanState::Waiting => {
                *state = CanState::Mission;
                Ok(())
            }
            CanState::Mission => Err(anyhow::anyhow!("Mission already started")),
            _ => Err(anyhow::anyhow!("Invalid state transition")),
        }
    }

    /// Transitions from Mission to Landing state.
    ///
    /// # Errors
    ///
    /// Returns an error if called when not in Mission state or if
    /// the transition is otherwise invalid.
    pub fn start_landing(&mut self) -> anyhow::Result<()> {
        let mut state = self.state.lock().unwrap();
        match *state {
            CanState::Mission => {
                *state = CanState::Landing;
                Ok(())
            }
            CanState::Landing => Err(anyhow::anyhow!("Landing already started")),
            _ => Err(anyhow::anyhow!("Invalid state transition")),
        }
    }

    /// Transitions from Landing to Stopping state.
    ///
    /// # Errors
    ///
    /// Returns an error if called when not in Landing state or if
    /// the transition is otherwise invalid.
    pub fn start_stopping(&mut self) -> anyhow::Result<()> {
        let mut state = self.state.lock().unwrap();
        match *state {
            CanState::Landing => {
                *state = CanState::Stopping;
                Ok(())
            }
            CanState::Stopping => Err(anyhow::anyhow!("Stopping already started")),
            _ => Err(anyhow::anyhow!("Invalid state transition")),
        }
    }

    /// Returns the current state of the CanSat.
    ///
    /// This method acquires a lock on the state mutex and returns a copy
    /// of the current state.
    pub fn get_state(&self) -> CanState {
        self.state.lock().unwrap().clone()
    }
}

unsafe impl Send for StateMachine {}

unsafe impl Sync for StateMachine {}

/// Macro for changing the state of the CanSat.
///
/// This macro handles the state transition, logging, and sequence incrementing.
///
/// # Arguments
///
/// * `globals` - The global variables containing the state machine and other resources.
/// * `state` - The state to transition to in format `start_`.
///

#[macro_export]
macro_rules! change_state {
    ($globals:expr, $state:ident) => {
        let previous_state = $globals.state_machine.get_state();
        match $globals.state_machine.$state() {
            Ok(_) => {
                match $globals
                    .xbee_sender
                    .send(aeros::xbee::message::XbeeMessage::new(
                        aeros::xbee::systems::XbeeSystems::ComputeModule(
                            aeros::xbee::components::ComputeModuleComponents::StateMachine,
                        ),
                        methlink::MavMessage::CAN_STATE_CHANGE(methlink::CAN_STATE_CHANGE_DATA {
                            from_state: aeros::xbee::converters::state_from_pseudo(&previous_state),
                            to_state: aeros::xbee::converters::state_from_pseudo(
                                &$globals.state_machine.get_state(),
                            ),
                        }),
                    )) {
                    Ok(_) => log::info!("State change message sent"),
                    Err(e) => log::error!("Failed to send state change message: {}", e),
                }
                $globals.sequences.increment_sequence(
                    &aeros::xbee::systems::XbeeSystems::ComputeModule(
                        aeros::xbee::components::ComputeModuleComponents::StateMachine,
                    ),
                );
                log::info!("{} started", stringify!($state));
            }
            Err(e) => {
                log::error!("Failed to start {}: {}", stringify!($state), e);
                match $globals
                    .xbee_sender
                    .send(aeros::xbee::message::XbeeMessage::new(
                        aeros::xbee::systems::XbeeSystems::ComputeModule(
                            aeros::xbee::components::ComputeModuleComponents::StateMachine,
                        ),
                        methlink::MavMessage::ERROR(methlink::ERROR_DATA {
                            error_code: methlink::Error::ERROR_INVALID_STATE_TRANSITION,
                        }),
                    )) {
                    Ok(_) => log::info!("Error message sent"),
                    Err(e) => log::error!("Failed to send error message: {}", e),
                }
                $globals.sequences.increment_sequence(
                    &aeros::xbee::systems::XbeeSystems::ComputeModule(
                        aeros::xbee::components::ComputeModuleComponents::StateMachine,
                    ),
                );
            }
        }
    };
    ($globals:expr, $state:ident) => {
        match $globals.state_machine.$state() {
            Ok(_) => log::info!("{} started", stringify!($state)),
            Err(e) => log::error!("Failed to start {}: {}", stringify!($state), e),
        }
    };
}
