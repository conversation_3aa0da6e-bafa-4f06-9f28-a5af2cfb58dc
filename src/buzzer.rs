//! This module is to interface with the buzzer hardware.
//!
//! We are using the Jiangsu Huaneng Elec MLT-8530 buzzer.
//!
//! ## Links
//! - [Datasheet](https://www.lcsc.com/product-detail/Buzzers_Jiangsu-Huaneng-Elec-MLT-8530_C94599.html?s_z=n_mlt8530)

use crate::globals;

#[cfg(feature = "mock")]
use log::warn;

#[cfg(not(feature = "mock"))]
use log::{error, info};

#[cfg(not(feature = "mock"))]
use crate::{
    methlink,
    xbee::{components, message, systems},
};

#[cfg(not(feature = "mock"))]
use rppal::pwm::{Channel, Polarity, Pwm};

#[cfg(feature = "mock")]
use rppal::pwm::Pwm;

#[allow(unused_variables)]
/// Initializes the buzzer.
pub fn initialize_buzzer(globals: &globals::Globals) -> Result<Option<Pwm>, anyhow::Error> {
    #[cfg(feature = "mock")]
    {
        warn!("Running in mock mode, buzzer not initialized");
        return Ok(None);
    }

    #[cfg(not(feature = "mock"))]
    {
        let buzzer = match Pwm::with_frequency(
            match globals.config.buzzer.channel {
                0 => Channel::Pwm0,
                1 => Channel::Pwm1,
                _ => {
                    error!("Invalid buzzer channel: {}", globals.config.buzzer.channel);
                    globals.xbee_sender.send(message::XbeeMessage::new(
                        systems::XbeeSystems::ComputeModule(
                            components::ComputeModuleComponents::Buzzer,
                        ),
                        methlink::MavMessage::ERROR(methlink::ERROR_DATA {
                            error_code: methlink::Error::ERROR_INVALID_BUZZER_CHANNEL,
                        }),
                    ))?;
                    Channel::Pwm0
                }
            },
            globals.config.buzzer.frequency,
            globals.config.buzzer.duty_cycle,
            Polarity::Normal,
            true,
        ) {
            Ok(buzzer) => Some(buzzer),
            Err(e) => {
                error!("Failed to initialize buzzer: {}", e);
                globals.xbee_sender.send(message::XbeeMessage::new(
                    systems::XbeeSystems::ComputeModule(
                        components::ComputeModuleComponents::Buzzer,
                    ),
                    methlink::MavMessage::ERROR(methlink::ERROR_DATA {
                        error_code: methlink::Error::ERROR_BUZZER_INITIALIZATION_FAILED,
                    }),
                ))?;
                None
            }
        };

        Ok(buzzer)
    }
}

#[allow(unused_variables)]
/// Starts the buzzer.
pub fn start_bussin(buzzer: &Option<Pwm>, globals: &globals::Globals) -> Result<(), anyhow::Error> {
    #[cfg(feature = "mock")]
    {
        warn!("Buzzer would be enabled here");
        return Ok(());
    }

    #[cfg(not(feature = "mock"))]
    {
        if let Some(buzzer_ref) = buzzer {
            match buzzer_ref.enable() {
                Ok(_) => info!("Buzzer enabled"),
                Err(e) => {
                    error!("Failed to enable buzzer: {}", e);
                    globals.xbee_sender.send(message::XbeeMessage::new(
                        systems::XbeeSystems::ComputeModule(
                            components::ComputeModuleComponents::Buzzer,
                        ),
                        methlink::MavMessage::ERROR(methlink::ERROR_DATA {
                            error_code: methlink::Error::ERROR_BUZZER_ENABLE_FAILED,
                        }),
                    ))?;
                }
            }
        } else {
            error!("Cannot enable buzzer: not initialized");
            globals.xbee_sender.send(message::XbeeMessage::new(
                systems::XbeeSystems::ComputeModule(components::ComputeModuleComponents::Buzzer),
                methlink::MavMessage::ERROR(methlink::ERROR_DATA {
                    error_code: methlink::Error::ERROR_BUZZER_ENABLE_FAILED,
                }),
            ))?;
        }

        Ok(())
    }
}

#[allow(unused_variables)]
/// Stops the buzzer.
pub fn stop_bussin(buzzer: &Option<Pwm>, globals: &globals::Globals) -> Result<(), anyhow::Error> {
    #[cfg(feature = "mock")]
    {
        warn!("Buzzer would be disabled here");
        return Ok(());
    }

    #[cfg(not(feature = "mock"))]
    {
        if let Some(buzzer_ref) = buzzer {
            match buzzer_ref.disable() {
                Ok(_) => info!("Buzzer disabled"),
                Err(e) => {
                    error!("Failed to disable buzzer: {}", e);
                    globals.xbee_sender.send(message::XbeeMessage::new(
                        systems::XbeeSystems::ComputeModule(
                            components::ComputeModuleComponents::Buzzer,
                        ),
                        methlink::MavMessage::ERROR(methlink::ERROR_DATA {
                            error_code: methlink::Error::ERROR_BUZZER_DISABLE_FAILED,
                        }),
                    ))?;
                }
            }
        } else {
            error!("Cannot disable buzzer: not initialized");
            globals.xbee_sender.send(message::XbeeMessage::new(
                systems::XbeeSystems::ComputeModule(components::ComputeModuleComponents::Buzzer),
                methlink::MavMessage::ERROR(methlink::ERROR_DATA {
                    error_code: methlink::Error::ERROR_BUZZER_DISABLE_FAILED,
                }),
            ))?;
        }

        Ok(())
    }
}
