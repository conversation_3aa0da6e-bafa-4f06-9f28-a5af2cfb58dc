use crate::config::Config;
use crate::states::StateMachine;
use crate::xbee::message::XbeeMessage;
use crate::xbee::sequences::XbeeSequences;

/// Global variables for the application.
#[derive(Debug, Clone)]
pub struct Globals {
    pub mission_path: String,
    pub config: Config,
    pub state_machine: StateMachine,
    pub sequences: XbeeSequences,
    pub xbee_sender: std::sync::mpsc::Sender<XbeeMessage>,
}

impl Globals {
    pub fn new(
        mission_path: String,
        state_machine: StateMachine,
        config: Config,
        xbee_sender: std::sync::mpsc::Sender<XbeeMessage>,
    ) -> Globals {
        Globals {
            mission_path: mission_path,
            config: config,
            state_machine: state_machine,
            sequences: XbeeSequences::default(),
            xbee_sender: xbee_sender,
        }
    }
}
