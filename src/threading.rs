//! This module contains the threading functionality for the CanSat.

use linux_embedded_hal::serial_core::SerialPort;
use log::warn;

use crate::globals::Globals;
use crate::methlink;
use crate::sensors::bme280::Bme280;
use crate::sensors::camera::save_frame;
use crate::sensors::systemstats::SystemStats;
use crate::sensors::thermal_camera::ThermalCamera;
use crate::states::CanState;
use crate::xbee::{components, device, message, systems};

use std::fs;
use std::sync::mpsc;
use std::thread;
use std::time::Duration;

use libcamera::{
    camera::CameraConfigurationStatus,
    camera_manager::CameraManager,
    framebuffer::AsFrameBuffer,
    framebuffer_allocator::{FrameBuffer, FrameBufferAllocator},
    framebuffer_map::MemoryMappedFrameBuffer,
    geometry::Size,
    pixel_format::PixelFormat,
    request::ReuseFlag,
    stream::StreamRole,
};

use std::cmp::Ordering;

use log::{error, info};

/// Thermal camera thread that writes data to a CSV file.
pub fn thermal_camera_thread(globals: Globals) {
    let file = format!("{}/thermal_camera.csv", globals.mission_path);
    let mut writer = csv_writer::CSVWriter::new(
        file,
        vec![
            "index".to_string(),
            "datetime".to_string(),
            "thermal_image".to_string(),
        ],
        match ThermalCamera::new(&globals.config.thermal_camera) {
            Ok(camera) => camera,
            Err(e) => {
                globals
                    .xbee_sender
                    .send(message::XbeeMessage::new(
                        systems::XbeeSystems::ComputeModule(
                            components::ComputeModuleComponents::ThermalCamera,
                        ),
                        methlink::MavMessage::ERROR(methlink::ERROR_DATA {
                            error_code: methlink::Error::ERROR_THERMAL_CAMERA_INITIALIZATION_FAILED,
                        }),
                    ))
                    .unwrap();
                log::error!("Failed to initialize thermal camera: {}", e);
                return;
            }
        },
    );

    writer.write_header();

    thread::sleep(std::time::Duration::from_millis(16));

    while globals.state_machine.get_state() != CanState::Stopping {
        writer.refresh_data();
        writer.write_data();
        thread::sleep(std::time::Duration::from_millis(
            ((1000 / globals.config.thermal_camera.fps) - 15) as u64,
        ));
    }
}

/// System stats thread that writes data to a CSV file.
pub fn system_stats_thread(globals: Globals) {
    let file = format!("{}/system_stats.csv", globals.mission_path);
    let mut writer = csv_writer::CSVWriter::new(
        file,
        vec![
            "index".to_string(),
            "datetime".to_string(),
            "cpu_percent".to_string(),
            "memory_percent".to_string(),
            "cpu_temp".to_string(),
            "cpu_clock".to_string(),
        ],
        SystemStats::new(),
    );

    writer.write_header();

    thread::sleep(std::time::Duration::from_millis(500));

    while globals.state_machine.get_state() != CanState::Stopping {
        writer.refresh_data();

        match writer
            .data
            .cpu_percentage
            .partial_cmp(&globals.config.system_stats.cpu_usage_threshold)
        {
            Some(Ordering::Greater) => {
                globals
                    .xbee_sender
                    .send(message::XbeeMessage::new(
                        systems::XbeeSystems::ComputeModule(
                            components::ComputeModuleComponents::SystemStats,
                        ),
                        methlink::MavMessage::WARNING(methlink::WARNING_DATA {
                            warning_code: methlink::Warning::WARNING_CPU_USAGE_HIGH,
                        }),
                    ))
                    .unwrap();
                warn!("CPU usage is too high");
            }
            _ => {}
        }

        match writer
            .data
            .cpu_temperature
            .partial_cmp(&globals.config.system_stats.cpu_temperature_threshold)
        {
            Some(Ordering::Greater) => {
                globals
                    .xbee_sender
                    .send(message::XbeeMessage::new(
                        systems::XbeeSystems::ComputeModule(
                            components::ComputeModuleComponents::SystemStats,
                        ),
                        methlink::MavMessage::WARNING(methlink::WARNING_DATA {
                            warning_code: methlink::Warning::WARNING_CPU_TEMPERATURE_HIGH,
                        }),
                    ))
                    .unwrap();
                warn!("CPU temperature is too high");
            }
            _ => {}
        }

        match writer
            .data
            .memory_percentage
            .partial_cmp(&globals.config.system_stats.memory_usage_threshold)
        {
            Some(Ordering::Greater) => {
                globals
                    .xbee_sender
                    .send(message::XbeeMessage::new(
                        systems::XbeeSystems::ComputeModule(
                            components::ComputeModuleComponents::SystemStats,
                        ),
                        methlink::MavMessage::WARNING(methlink::WARNING_DATA {
                            warning_code: methlink::Warning::WARNING_MEMORY_USAGE_HIGH,
                        }),
                    ))
                    .unwrap();
                warn!("Memory usage is too high");
            }
            _ => {}
        }

        writer.write_data();
        thread::sleep(std::time::Duration::from_millis(500));
    }
}

pub fn xbee_thread(globals: Globals, xbee_receiver: mpsc::Receiver<message::XbeeMessage>) {
    let mut xbee_device = device::XbeeDevice::new(
        &globals.config.xbee.port,
        globals.config.xbee.baud_rate,
        &globals.mission_path,
    )
    .unwrap();

    while globals.state_machine.get_state() != CanState::Stopping {
        let message = match xbee_receiver.recv() {
            Ok(message) => message,
            Err(e) => {
                log::error!("All Xbee senders disconnected: {}", e);
                return;
            }
        };
        match message.cook(&mut xbee_device, globals.clone()) {
            Ok(_) => log::info!("Message sent"),
            Err(e) => log::error!("Failed to send message: {}", e),
        }
    }
}

const PIXEL_FORMAT_YUYV: PixelFormat =
    PixelFormat::new(u32::from_le_bytes([b'Y', b'U', b'Y', b'V']), 0);

pub fn color_camera_thread(mut globals: Globals) {
    let color_dir = format!("{}/color_camera", globals.mission_path);
    match fs::create_dir_all(&color_dir) {
        Ok(_) => info!("Color camera directory created"),
        Err(e) => {
            error!("Failed to create color camera directory: {}", e);
            return;
        }
    }

    let manager = CameraManager::new().unwrap();

    let mut cameras = manager.cameras();

    while cameras.is_empty() {
        info!("No cameras found, waiting for camera to be detected");
        if globals.state_machine.get_state() == CanState::Stopping {
            info!("Stopping color camera thread due to stopping state");
            return;
        }
        thread::sleep(Duration::from_secs(1));
        cameras = manager.cameras();
    }

    let cam = cameras.get(0).unwrap();

    let mut cam = cam.acquire().expect("Failed to acquire camera");

    let mut cfgs = cam
        .generate_configuration(&[StreamRole::ViewFinder])
        .unwrap();

    // Set resolution before pixel format
    let mut config = cfgs.get_mut(0).unwrap();
    config.set_size(Size {
        width: globals.config.color_camera.width,
        height: globals.config.color_camera.height,
    });
    config.set_pixel_format(PIXEL_FORMAT_YUYV);

    match cfgs.validate() {
        CameraConfigurationStatus::Valid => info!("Camera configuration valid!"),
        CameraConfigurationStatus::Adjusted => {
            info!("Camera configuration was adjusted: {:#?}", cfgs);
            // Get the adjusted dimensions
            let adjusted_config = cfgs.get(0).unwrap();
            let adjusted_size = adjusted_config.get_size();
            info!(
                "Adjusted dimensions: {}x{}",
                adjusted_size.width, adjusted_size.height
            );
            // Update our width and height to match what the camera actually gave us
            globals.config.color_camera.width = adjusted_size.width;
            globals.config.color_camera.height = adjusted_size.height;
        }
        CameraConfigurationStatus::Invalid => {
            error!("Camera configuration is invalid: {:#?}", cfgs);
            return;
        }
    }

    cam.configure(&mut cfgs)
        .expect("Unable to configure camera");

    let mut alloc = FrameBufferAllocator::new(&cam);

    // Allocate frame buffers for the stream
    let cfg = match cfgs.get(0) {
        Some(cfg) => cfg,
        None => {
            error!("No camera configuration found");
            return;
        }
    };

    info!("Camera configuration: {:#?}", cfg);

    let stream = cfg.stream().unwrap();
    let buffers = match alloc.alloc(&stream) {
        Ok(buffers) => buffers,
        Err(e) => {
            error!("Error allocating frame buffers: {}", e);
            return;
        }
    };
    info!("Allocated {} buffers", buffers.len());

    // Convert FrameBuffer to MemoryMappedFrameBuffer, which allows reading &[u8]
    let buffers = buffers
        .into_iter()
        .map(|buf| MemoryMappedFrameBuffer::new(buf).unwrap())
        .collect::<Vec<_>>();

    // Create capture requests and attach buffers
    let reqs = buffers
        .into_iter()
        .enumerate()
        .map(|(i, buf)| {
            let mut req = cam.create_request(Some(i as u64)).unwrap();
            req.add_buffer(&stream, buf).unwrap();
            req
        })
        .collect::<Vec<_>>();

    // Completed capture requests are returned as a callback
    let (tx, rx) = std::sync::mpsc::channel();
    cam.on_request_completed(move |req| match tx.send(req) {
        Ok(_) => info!("Request completed"),
        Err(e) => error!("Error sending request: {}", e),
    });

    match cam.start(None) {
        Ok(_) => info!("Camera started"),
        Err(e) => {
            error!("Failed to start camera: {}", e);
            return;
        }
    }

    // Enqueue all requests to the camera
    for req in reqs {
        info!("Request queued for execution: {req:#?}");
        cam.queue_request(req).unwrap();
    }

    libcamera::controls::FrameDurationLimits([
        1000 / globals.config.color_camera.fps as i64,
        1000 / globals.config.color_camera.fps as i64,
    ]); // Set frame duration limits to 33ms

    let mut frame_count = 0;

    while globals.state_machine.get_state() != CanState::Stopping {
        let mut req = rx
            .recv_timeout(Duration::from_secs(2))
            .expect("Camera request failed");

        // Get the memory mapped buffer from the request.
        let mapped_buf = req
            .buffer::<MemoryMappedFrameBuffer<FrameBuffer>>(&stream)
            .unwrap();

        // Copy the raw data (for the first plane) into an owned vector.
        // (You may also want to extract the bytes_used from the metadata if needed.)
        let data_copy = mapped_buf.data()[0].to_vec();
        let metadata = mapped_buf.metadata().unwrap();
        let bytes_used: usize = metadata
            .planes()
            .get(0)
            .unwrap()
            .bytes_used
            .try_into()
            .unwrap();

        // Clone color_dir and frame_count for use in the thread.
        let thread_color_dir = color_dir.clone();
        let thread_frame_count = frame_count;

        // Spawn a thread to save the frame using the owned data.
        thread::spawn(move || {
            save_frame(
                data_copy,
                bytes_used,
                thread_color_dir,
                thread_frame_count,
                globals.config.color_camera.width as usize,
                globals.config.color_camera.height as usize,
            )
            .unwrap();
        });

        frame_count += 1;

        req.reuse(ReuseFlag::REUSE_BUFFERS);
        cam.queue_request(req).unwrap();
    }

    info!("Color camera thread stopped");
}

pub fn bme280_thread(globals: Globals) {
    let file = format!("{}/bme280.csv", globals.mission_path);
    let mut writer = csv_writer::CSVWriter::new(
        file,
        vec![
            "index".to_string(),
            "datetime".to_string(),
            "temperature".to_string(),
            "pressure".to_string(),
            "humidity".to_string(),
        ],
        match Bme280::new(&globals.config.bme280) {
            Ok(bme280) => bme280,
            Err(e) => {
                globals
                    .xbee_sender
                    .send(message::XbeeMessage::new(
                        systems::XbeeSystems::ComputeModule(
                            components::ComputeModuleComponents::Bme280,
                        ),
                        methlink::MavMessage::ERROR(methlink::ERROR_DATA {
                            error_code: methlink::Error::ERROR_BME280_INITIALIZATION_FAILED,
                        }),
                    ))
                    .unwrap();
                log::error!("Failed to initialize BME280: {}", e);
                return;
            }
        },
    );

    writer.write_header();

    thread::sleep(std::time::Duration::from_millis(
        1000 / globals.config.bme280.fps,
    ));

    while globals.state_machine.get_state() != CanState::Stopping {
        writer.refresh_data();

        globals
            .xbee_sender
            .send(message::XbeeMessage::new(
                systems::XbeeSystems::ComputeModule(components::ComputeModuleComponents::Bme280),
                methlink::MavMessage::BME280(methlink::BME280_DATA {
                    temperature: writer.data.temperature,
                    pressure: writer.data.pressure,
                    humidity: writer.data.humidity,
                }),
            ))
            .unwrap();

        writer.write_data();
        thread::sleep(std::time::Duration::from_millis(
            1000 / globals.config.bme280.fps,
        ));
    }
}

pub fn gps_thread(globals: Globals) {
    let mut gps = match serialport::new(globals.config.gps.port, globals.config.gps.baud_rate)
        .open()
    {
        Ok(port) => {
            info!("GPS port opened");
            port
        }
        Err(e) => {
            error!("Failed to open GPS port: {}", e);
            globals
                .xbee_sender
                .send(message::XbeeMessage::new(
                    systems::XbeeSystems::ComputeModule(components::ComputeModuleComponents::GPS),
                    methlink::MavMessage::ERROR(methlink::ERROR_DATA {
                        error_code: methlink::Error::ERROR_GPS_INITIALIZATION_FAILED,
                    }),
                ))
                .unwrap();
            return;
        }
    };

    while globals.state_machine.get_state() != CanState::Stopping {
        let mut nmea = nmea::Nmea::default();

        let mut buffer = [0; 1024];
        match gps.read(&mut buffer) {
            Ok(size) => {
                info!("Read {} bytes from GPS", size);
                match nmea.parse(&buffer) {
                    Ok(nmea_message) => {
                        info!("Parsed NMEA message: {:?}", nmea_message);
                    }
                    Err(e) => {
                        error!("Failed to parse NMEA message: {}", e);
                    }
                }
                println!("NMEA: {:?}", nmea);
            }
            Err(e) => {
                error!("Failed to read from GPS: {}", e);
            }
        }

        thread::sleep(std::time::Duration::from_secs(1));
    }
}
